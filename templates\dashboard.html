<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SS Network Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        :root {
            --bs-dark: #0d1117;
            --bs-dark-rgb: 13, 17, 23;
            --bs-body-bg:rgb(0, 0, 0);
            --bs-body-color: #e6edf3;
            --bs-border-color: #30363d;
            --ss-red: #dc2626;
            --ss-red-dark: #b91c1c;
            --ss-red-light: #ef4444;
        }

        body {
            background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 100%) !important;
            border-bottom: 2px solid var(--ss-red-light);
            box-shadow: 0 2px 10px rgba(220, 38, 38, 0.3);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .card {
            background: rgba(22, 27, 34, 0.8);
            border: 1px solid var(--ss-red);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(220, 38, 38, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(220, 38, 38, 0.3);
        }

        .card-header {
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 100%);
            border-bottom: 1px solid var(--ss-red-light);
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(45deg, var(--ss-red-dark) 0%, var(--ss-red) 100%);
            border: none;
            box-shadow: 0 2px 10px rgba(220, 38, 38, 0.3);
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(45deg, var(--ss-red) 0%, var(--ss-red-light) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
        }

        .btn-outline-danger {
            border-color: var(--ss-red);
            color: var(--ss-red);
        }

        .btn-outline-danger:hover {
            background-color: var(--ss-red);
            border-color: var(--ss-red);
        }

        .table-dark {
            --bs-table-bg: rgba(22, 27, 34, 0.6);
            --bs-table-border-color: var(--ss-red);
        }

        .table-dark th {
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 100%);
            border-color: var(--ss-red-light);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5em 0.75em;
        }

        .status-connected {
            background: linear-gradient(45deg, #059669, #10b981);
            animation: pulse 2s infinite;
        }

        .status-disconnected {
            background: linear-gradient(45deg, #dc2626, #ef4444);
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
            100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
        }

        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(13, 17, 23, 0.8);
            border: 1px solid var(--ss-red);
            border-radius: 8px;
            padding: 15px;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-info { border-left-color: #3b82f6; background: rgba(59, 130, 246, 0.1); }
        .log-success { border-left-color: #10b981; background: rgba(16, 185, 129, 0.1); }
        .log-warning { border-left-color: #f59e0b; background: rgba(245, 158, 11, 0.1); }
        .log-error { border-left-color: #ef4444; background: rgba(239, 68, 68, 0.1); }
        .log-command { border-left-color: var(--ss-red); background: rgba(220, 38, 38, 0.1); }
        .log-response { border-left-color: #8b5cf6; background: rgba(139, 92, 246, 0.1); }

        .stats-card {
            text-align: center;
            padding: 20px;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--ss-red);
            text-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
        }

        .form-control:focus {
            border-color: var(--ss-red);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }

        .form-select:focus {
            border-color: var(--ss-red);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }

        .server-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .server-online {
            background: #10b981;
            animation: pulse 2s infinite;
        }

        .server-offline {
            background: #ef4444;
        }

        .command-history {
            max-height: 300px;
            overflow-y: auto;
        }

        .command-item {
            background: rgba(22, 27, 34, 0.6);
            border: 1px solid var(--bs-border-color);
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .command-item:hover {
            background: rgba(22, 27, 34, 0.8);
            border-color: var(--ss-red);
            transform: translateX(5px);
        }

        .output-viewer {
            background: rgba(13, 17, 23, 0.95);
            border: 2px solid var(--ss-red);
            border-radius: 12px;
            max-height: 600px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .output-header {
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 100%);
            padding: 12px 16px;
            border-radius: 10px 10px 0 0;
            border-bottom: 1px solid var(--ss-red-light);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .output-content {
            padding: 16px;
            white-space: pre-wrap;
            word-wrap: break-word;
            background: rgba(13, 17, 23, 0.8);
        }

        .output-item {
            background: rgba(22, 27, 34, 0.7);
            border: 1px solid var(--bs-border-color);
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .output-item:hover {
            border-color: var(--ss-red);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
        }

        .output-item-header {
            background: rgba(22, 27, 34, 0.9);
            padding: 10px 15px;
            border-bottom: 1px solid var(--bs-border-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .output-item-content {
            padding: 15px;
            background: rgba(13, 17, 23, 0.6);
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .output-item-content.show {
            display: block;
        }

        .floating-output {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 900px;
            max-height: 80vh;
            z-index: 1050;
            background: rgba(13, 17, 23, 0.98);
            border: 2px solid var(--ss-red);
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .floating-output-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1040;
            backdrop-filter: blur(5px);
        }

        .terminal-output {
            background: #0a0a0a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
        }

        .command-badge {
            background: linear-gradient(45deg, var(--ss-red-dark), var(--ss-red));
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .client-badge {
            background: linear-gradient(45deg, #1f2937, #374151);
            color: #e5e7eb;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
        }

        .expand-btn {
            background: none;
            border: none;
            color: var(--ss-red);
            font-size: 1.2rem;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .expand-btn:hover {
            transform: scale(1.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .enhanced-card {
            background: linear-gradient(135deg, rgba(22, 27, 34, 0.9) 0%, rgba(13, 17, 23, 0.9) 100%);
            border: 1px solid var(--ss-red);
            border-radius: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .enhanced-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 50%, var(--ss-red-light) 100%);
        }

        .enhanced-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(220, 38, 38, 0.3);
        }

        .ss-logo {
            font-family: 'Times New Roman', serif;
            font-weight: bold;
            letter-spacing: 2px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand text-white ss-logo" href="#">
                <i class="fas fa-skull-crossbones me-2"></i>
                SS Network ᛋᛋ Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white me-3">
                    <span id="dashboard-status" class="server-status server-online"></span>
                    Dashboard: <span id="dashboard-status-text">Connected</span>
                </span>
                <span class="navbar-text text-white">
                    <span id="server-status" class="server-status server-offline"></span>
                    Server: <span id="server-status-text">Offline</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Enhanced Stats Grid -->
        <div class="stats-grid">
            <div class="enhanced-card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-skull-crossbones fa-2x text-danger me-3"></i>
                        <div>
                            <div class="stats-number" id="total-clients">0</div>
                            <div class="text-muted">Total Souls</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="enhanced-card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-wifi fa-2x text-success me-3"></i>
                        <div>
                            <div class="stats-number text-success" id="connected-clients">0</div>
                            <div class="text-muted">Connected</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="enhanced-card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-terminal fa-2x text-warning me-3"></i>
                        <div>
                            <div class="stats-number text-warning" id="total-commands">0</div>
                            <div class="text-muted">Commands Sent</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="enhanced-card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-eye fa-2x text-info me-3"></i>
                        <div>
                            <div class="stats-number text-info" id="total-outputs">0</div>
                            <div class="text-muted">Command Outputs</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="enhanced-card stats-card">
                <div class="card-body text-center">
                    <button class="btn btn-danger w-100 mb-2" id="start-server-btn" onclick="startServer()">
                        <i class="fas fa-play me-2"></i>Start Server
                    </button>
                    <button class="btn btn-outline-danger w-100 d-none" id="stop-server-btn" onclick="stopServer()">
                        <i class="fas fa-stop me-2"></i>Stop Server
                    </button>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Connected Clients -->
            <div class="col-lg-8">
                <div class="enhanced-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>Connected Souls
                            <span class="badge bg-danger ms-2" id="clients-count-badge">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-globe me-1"></i>IP Address</th>
                                        <th><i class="fas fa-plug me-1"></i>Port</th>
                                        <th><i class="fas fa-clock me-1"></i>Connected At</th>
                                        <th><i class="fas fa-heartbeat me-1"></i>Last Seen</th>
                                        <th><i class="fas fa-signal me-1"></i>Status</th>
                                        <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="clients-table">
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="fas fa-ghost fa-3x mb-3 d-block"></i>
                                            No souls connected
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Command Panel -->
            <div class="col-lg-4">
                <div class="enhanced-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>Command Center
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="command-input" class="form-label">
                                <i class="fas fa-keyboard me-1"></i>Command
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark border-danger text-danger">
                                    <i class="fas fa-dollar-sign"></i>
                                </span>
                                <input type="text" class="form-control" id="command-input" placeholder="Enter command...">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="target-select" class="form-label">
                                <i class="fas fa-crosshairs me-1"></i>Target
                            </label>
                            <select class="form-select" id="target-select">
                                <option value="broadcast">🌐 Broadcast to All</option>
                            </select>
                        </div>
                        <button class="btn btn-danger w-100 mb-2" onclick="sendCommand()">
                            <i class="fas fa-paper-plane me-2"></i>Execute Command
                        </button>
                        <button class="btn btn-outline-info w-100" onclick="showOutputViewer()">
                            <i class="fas fa-eye me-2"></i>View All Outputs
                        </button>
                    </div>
                </div>

                <!-- Quick Commands -->
                <div class="enhanced-card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Commands
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('whoami')">
                                <i class="fas fa-user me-2"></i>Who Am I
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('ipconfig /all')">
                                <i class="fas fa-network-wired me-2"></i>IP Config
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('getip')">
                                <i class="fas fa-globe me-2"></i>Get Public IP
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('systeminfo')">
                                <i class="fas fa-info-circle me-2"></i>System Info
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('tasklist')">
                                <i class="fas fa-list me-2"></i>Task List
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Command Outputs Section -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="enhanced-card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-desktop me-2"></i>Command Outputs
                                <span class="badge bg-info ms-2" id="outputs-count-badge">0</span>
                            </h5>
                            <div>
                                <button class="btn btn-outline-danger btn-sm me-2" onclick="clearOutputs()">
                                    <i class="fas fa-trash me-1"></i>Clear
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="refreshOutputs()">
                                    <i class="fas fa-sync me-1"></i>Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="command-outputs-container">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-terminal fa-3x mb-3 d-block"></i>
                                No command outputs yet
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs and Command History -->
        <div class="row mt-4">
            <div class="col-lg-8">
                <div class="enhanced-card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list-alt me-2"></i>System Logs
                                <span class="badge bg-secondary ms-2" id="logs-count-badge">0</span>
                            </h5>
                            <button class="btn btn-outline-danger btn-sm" onclick="clearLogs()">
                                <i class="fas fa-broom me-1"></i>Clear Logs
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="log-container" id="logs-container">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-file-alt fa-3x mb-3 d-block"></i>
                                No logs available
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="enhanced-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>Command History
                            <span class="badge bg-warning ms-2" id="history-count-badge">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="command-history" id="command-history">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-clock fa-2x mb-3 d-block"></i>
                                No commands executed
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Output Viewer Modal -->
        <div id="output-viewer-overlay" class="floating-output-overlay d-none" onclick="hideOutputViewer()"></div>
        <div id="output-viewer" class="floating-output d-none">
            <div class="output-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-terminal me-2"></i>Command Output Viewer
                    </h5>
                    <button class="btn btn-outline-light btn-sm" onclick="hideOutputViewer()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="output-content" id="floating-output-content">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const socket = io();
        let serverRunning = false;
        let totalCommands = 0;
        let commandOutputs = [];
        let totalOutputs = 0;

        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to dashboard server');
            updateDashboardStatus(true);
            // Request full update when connecting/reconnecting
            socket.emit('request_full_update');
        });

        socket.on('disconnect', function() {
            console.log('Disconnected from dashboard server');
            updateDashboardStatus(false);
            // Show connection status
            addLogEntry({
                timestamp: new Date().toLocaleString(),
                message: 'Disconnected from dashboard server',
                level: 'warning'
            });
        });

        socket.on('reconnect', function() {
            console.log('Reconnected to dashboard server');
            updateDashboardStatus(true);
            // Request full update on reconnection
            socket.emit('request_full_update');
            addLogEntry({
                timestamp: new Date().toLocaleString(),
                message: 'Reconnected to dashboard server',
                level: 'success'
            });
        });

        socket.on('client_update', function(data) {
            updateClientsTable(data.clients);
            updateStats(data.clients);
            updateTargetSelect(data.clients);

            // Update server state if provided
            if (data.server_state) {
                updateServerStatus(data.server_state.running);
            }
        });

        socket.on('server_status_update', function(data) {
            updateServerStatus(data.running);
            if (data.server_state) {
                console.log('Server state updated:', data.server_state);
            }
        });

        socket.on('log_update', function(data) {
            // Clear existing logs and add all logs from server
            const container = document.getElementById('logs-container');
            container.innerHTML = '';
            data.logs.forEach(log => addLogEntry(log));
        });

        socket.on('command_history_update', function(data) {
            updateCommandHistory(data.history);
        });

        socket.on('new_log', function(log) {
            addLogEntry(log);
        });

        socket.on('command_response', function(data) {
            // Add to command outputs
            const outputData = {
                output_id: data.output_id,
                client: data.client,
                response: data.response,
                timestamp: data.timestamp,
                command: data.command || 'Unknown'
            };

            commandOutputs.unshift(outputData); // Add to beginning
            totalOutputs++;

            // Keep only last 50 outputs
            if (commandOutputs.length > 50) {
                commandOutputs = commandOutputs.slice(0, 50);
            }

            updateCommandOutputs();
            updateStats();

            addLogEntry({
                timestamp: new Date().toLocaleString(),
                message: `Response from ${data.client}: ${data.response.substring(0, 100)}${data.response.length > 100 ? '...' : ''}`,
                level: 'response'
            });
        });

        // Server control functions
        function startServer() {
            fetch('/api/server/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        serverRunning = true;
                        updateServerStatus(true);
                        addLogEntry({
                            timestamp: new Date().toLocaleString(),
                            message: 'Server started successfully',
                            level: 'success'
                        });
                    } else {
                        addLogEntry({
                            timestamp: new Date().toLocaleString(),
                            message: 'Failed to start server: ' + data.message,
                            level: 'error'
                        });
                    }
                });
        }

        function stopServer() {
            fetch('/api/server/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    serverRunning = false;
                    updateServerStatus(false);
                    addLogEntry({
                        timestamp: new Date().toLocaleString(),
                        message: 'Server stopped',
                        level: 'warning'
                    });
                });
        }

        function updateDashboardStatus(connected) {
            const statusIndicator = document.getElementById('dashboard-status');
            const statusText = document.getElementById('dashboard-status-text');

            if (connected) {
                statusIndicator.className = 'server-status server-online';
                statusText.textContent = 'Connected';
            } else {
                statusIndicator.className = 'server-status server-offline';
                statusText.textContent = 'Disconnected';
            }
        }

        function updateServerStatus(running) {
            const statusIndicator = document.getElementById('server-status');
            const statusText = document.getElementById('server-status-text');
            const startBtn = document.getElementById('start-server-btn');
            const stopBtn = document.getElementById('stop-server-btn');

            serverRunning = running;

            if (running) {
                statusIndicator.className = 'server-status server-online';
                statusText.textContent = 'Online';
                startBtn.classList.add('d-none');
                stopBtn.classList.remove('d-none');
            } else {
                statusIndicator.className = 'server-status server-offline';
                statusText.textContent = 'Offline';
                startBtn.classList.remove('d-none');
                stopBtn.classList.add('d-none');
            }
        }

        function updateCommandHistory(history) {
            const container = document.getElementById('command-history');

            if (history.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-clock fa-2x mb-3 d-block"></i>
                        No commands executed
                    </div>`;
                return;
            }

            container.innerHTML = history.map(cmd => `
                <div class="command-item" onclick="showCommandOutputs('${cmd.id || ''}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="command-badge">${cmd.command}</span>
                            <div class="text-muted small mt-1">
                                ${cmd.type === 'broadcast' ?
                                    `🌐 Broadcast (${cmd.target_count || 0} targets)` :
                                    `🎯 To: ${cmd.target}`}
                            </div>
                        </div>
                        <small class="text-muted">${cmd.timestamp}</small>
                    </div>
                </div>
            `).join('');

            document.getElementById('history-count-badge').textContent = history.length;
        }

        function updateCommandOutputs() {
            const container = document.getElementById('command-outputs-container');

            if (commandOutputs.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-terminal fa-3x mb-3 d-block"></i>
                        No command outputs yet
                    </div>`;
                return;
            }

            container.innerHTML = commandOutputs.map(output => `
                <div class="output-item">
                    <div class="output-item-header" onclick="toggleOutput('${output.output_id}')">
                        <div class="d-flex align-items-center">
                            <span class="client-badge me-2">${output.client}</span>
                            <span class="command-badge me-2">${output.command}</span>
                            <small class="text-muted">${output.timestamp}</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-outline-info btn-sm me-2" onclick="event.stopPropagation(); showFullOutput('${output.output_id}')" title="View Full Output">
                                <i class="fas fa-expand"></i>
                            </button>
                            <button class="expand-btn" id="expand-${output.output_id}">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                    <div class="output-item-content" id="content-${output.output_id}">
                        <div class="terminal-output">${escapeHtml(output.response)}</div>
                    </div>
                </div>
            `).join('');
        }

        function toggleOutput(outputId) {
            const content = document.getElementById(`content-${outputId}`);
            const expandBtn = document.getElementById(`expand-${outputId}`);

            if (content.classList.contains('show')) {
                content.classList.remove('show');
                expandBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
            } else {
                content.classList.add('show');
                expandBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
            }
        }

        function showFullOutput(outputId) {
            const output = commandOutputs.find(o => o.output_id === outputId);
            if (!output) return;

            const content = document.getElementById('floating-output-content');
            content.innerHTML = `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="client-badge">${output.client}</span>
                        <span class="command-badge">${output.command}</span>
                        <small class="text-muted">${output.timestamp}</small>
                    </div>
                </div>
                <div class="terminal-output">${escapeHtml(output.response)}</div>
            `;

            document.getElementById('output-viewer-overlay').classList.remove('d-none');
            document.getElementById('output-viewer').classList.remove('d-none');
        }

        function showOutputViewer() {
            const content = document.getElementById('floating-output-content');
            content.innerHTML = `
                <div class="mb-3">
                    <h6>All Command Outputs (${commandOutputs.length})</h6>
                </div>
                ${commandOutputs.map(output => `
                    <div class="output-item mb-3">
                        <div class="output-item-header">
                            <div class="d-flex align-items-center">
                                <span class="client-badge me-2">${output.client}</span>
                                <span class="command-badge me-2">${output.command}</span>
                                <small class="text-muted">${output.timestamp}</small>
                            </div>
                        </div>
                        <div class="output-item-content show">
                            <div class="terminal-output">${escapeHtml(output.response)}</div>
                        </div>
                    </div>
                `).join('')}
            `;

            document.getElementById('output-viewer-overlay').classList.remove('d-none');
            document.getElementById('output-viewer').classList.remove('d-none');
        }

        function hideOutputViewer() {
            document.getElementById('output-viewer-overlay').classList.add('d-none');
            document.getElementById('output-viewer').classList.add('d-none');
        }

        function showClientOutputs(clientAddr) {
            const clientOutputs = commandOutputs.filter(o => o.client === clientAddr);
            const content = document.getElementById('floating-output-content');

            content.innerHTML = `
                <div class="mb-3">
                    <h6>Outputs for ${clientAddr} (${clientOutputs.length})</h6>
                </div>
                ${clientOutputs.length === 0 ?
                    '<div class="text-center text-muted py-4">No outputs for this client</div>' :
                    clientOutputs.map(output => `
                        <div class="output-item mb-3">
                            <div class="output-item-header">
                                <div class="d-flex align-items-center">
                                    <span class="command-badge me-2">${output.command}</span>
                                    <small class="text-muted">${output.timestamp}</small>
                                </div>
                            </div>
                            <div class="output-item-content show">
                                <div class="terminal-output">${escapeHtml(output.response)}</div>
                            </div>
                        </div>
                    `).join('')
                }
            `;

            document.getElementById('output-viewer-overlay').classList.remove('d-none');
            document.getElementById('output-viewer').classList.remove('d-none');
        }

        function clearOutputs() {
            if (confirm('Are you sure you want to clear all command outputs?')) {
                commandOutputs = [];
                updateCommandOutputs();
                updateStats();
            }
        }

        function refreshOutputs() {
            fetch('/api/command_outputs')
                .then(response => response.json())
                .then(outputs => {
                    commandOutputs = outputs;
                    totalOutputs = outputs.length;
                    updateCommandOutputs();
                    updateStats();
                })
                .catch(error => {
                    console.error('Error fetching command outputs:', error);
                });
        }

        function clearLogs() {
            if (confirm('Are you sure you want to clear all logs?')) {
                const container = document.getElementById('logs-container');
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-file-alt fa-3x mb-3 d-block"></i>
                        No logs available
                    </div>`;
                document.getElementById('logs-count-badge').textContent = '0';
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Command functions
        function sendCommand() {
            const command = document.getElementById('command-input').value.trim();
            const target = document.getElementById('target-select').value;

            if (!command) {
                alert('Please enter a command');
                return;
            }

            const endpoint = target === 'broadcast' ? '/api/command/broadcast' : '/api/command/single';
            const payload = target === 'broadcast' 
                ? { command: command }
                : { command: command, client: target };

            fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    totalCommands++;
                    document.getElementById('total-commands').textContent = totalCommands;
                    document.getElementById('command-input').value = '';
                    
                    addLogEntry({
                        timestamp: new Date().toLocaleString(),
                        message: `Command sent: ${command} (${target === 'broadcast' ? 'broadcast' : 'to ' + target})`,
                        level: 'command'
                    });
                } else {
                    addLogEntry({
                        timestamp: new Date().toLocaleString(),
                        message: 'Failed to send command: ' + (data.message || 'Unknown error'),
                        level: 'error'
                    });
                }
            });
        }

        function quickCommand(command) {
            document.getElementById('command-input').value = command;
            sendCommand();
        }

        // UI update functions
        function updateClientsTable(clients) {
            const tbody = document.getElementById('clients-table');
            
            if (clients.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No souls connected</td></tr>';
                return;
            }

            tbody.innerHTML = clients.map(client => `
                <tr>
                    <td>${client.ip}</td>
                    <td>${client.port}</td>
                    <td>${client.connected_at}</td>
                    <td>${client.last_seen}</td>
                    <td>
                        <span class="badge ${client.status === 'Connected' ? 'status-connected' : 'status-disconnected'}">
                            ${client.status}
                        </span>
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-danger btn-sm" onclick="selectClient('${client.address}')" title="Select Target">
                                <i class="fas fa-crosshairs"></i>
                            </button>
                            <button class="btn btn-outline-info btn-sm" onclick="showClientOutputs('${client.address}')" title="View Outputs">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function updateStats(clients) {
            if (clients) {
                const totalClients = clients.length;
                const connectedClients = clients.filter(c => c.status === 'Connected').length;

                document.getElementById('total-clients').textContent = totalClients;
                document.getElementById('connected-clients').textContent = connectedClients;
                document.getElementById('clients-count-badge').textContent = connectedClients;
            }

            document.getElementById('total-commands').textContent = totalCommands;
            document.getElementById('total-outputs').textContent = totalOutputs;
            document.getElementById('outputs-count-badge').textContent = commandOutputs.length;
        }

        function updateTargetSelect(clients) {
            const select = document.getElementById('target-select');
            const currentValue = select.value;
            
            select.innerHTML = '<option value="broadcast">Broadcast to All</option>';
            
            clients.forEach(client => {
                if (client.status === 'Connected') {
                    const option = document.createElement('option');
                    option.value = client.address;
                    option.textContent = `${client.ip}:${client.port}`;
                    select.appendChild(option);
                }
            });
            
            // Restore previous selection if still valid
            if (currentValue !== 'broadcast') {
                const option = Array.from(select.options).find(opt => opt.value === currentValue);
                if (option) {
                    select.value = currentValue;
                }
            }
        }

        function selectClient(address) {
            document.getElementById('target-select').value = address;
        }

        function addLogEntry(log) {
            const container = document.getElementById('logs-container');

            // Remove "no logs" message if present
            if (container.innerHTML.includes('No logs available')) {
                container.innerHTML = '';
            }

            const logDiv = document.createElement('div');
            logDiv.className = `log-entry log-${log.level}`;
            logDiv.innerHTML = `
                <small class="text-muted">[${log.timestamp}]</small>
                <span class="ms-2">${log.message}</span>
            `;

            container.appendChild(logDiv);
            container.scrollTop = container.scrollHeight;

            // Update logs count
            document.getElementById('logs-count-badge').textContent = container.children.length;

            // Keep only last 100 log entries
            while (container.children.length > 100) {
                container.removeChild(container.firstChild);
                document.getElementById('logs-count-badge').textContent = container.children.length;
            }
        }

        // Enter key support for command input
        document.getElementById('command-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendCommand();
            }
        });

        // Auto-refresh functionality
        function refreshDashboard() {
            // Check server status
            fetch('/api/server/status')
                .then(response => response.json())
                .then(data => {
                    updateServerStatus(data.running);
                    if (data.server_state) {
                        console.log('Server status refreshed:', data.server_state);
                    }
                })
                .catch(error => {
                    console.error('Error fetching server status:', error);
                });

            // Refresh clients data
            fetch('/api/clients')
                .then(response => response.json())
                .then(data => {
                    updateClientsTable(data.clients);
                    updateStats(data.clients);
                    updateTargetSelect(data.clients);
                    if (data.server_state) {
                        updateServerStatus(data.server_state.running);
                    }
                })
                .catch(error => {
                    console.error('Error fetching clients:', error);
                });
        }

        // Load initial data
        refreshDashboard();

        // Set up periodic refresh (every 5 seconds)
        setInterval(refreshDashboard, 5000);

        // Refresh on page visibility change (when user returns to tab)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('Page became visible, refreshing dashboard');
                refreshDashboard();
                // Request full update via socket
                socket.emit('request_full_update');
            }
        });

        // Load command history
        fetch('/api/command_history')
            .then(response => response.json())
            .then(history => {
                updateCommandHistory(history);
            })
            .catch(error => {
                console.error('Error fetching command history:', error);
            });

        // Load command outputs
        fetch('/api/command_outputs')
            .then(response => response.json())
            .then(outputs => {
                commandOutputs = outputs;
                totalOutputs = outputs.length;
                updateCommandOutputs();
                updateStats();
            })
            .catch(error => {
                console.error('Error fetching command outputs:', error);
            });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Escape key to close output viewer
            if (e.key === 'Escape') {
                hideOutputViewer();
            }
            // Ctrl+Enter to execute command
            if (e.ctrlKey && e.key === 'Enter') {
                sendCommand();
            }
        });
    </script>
</body>
</html>
