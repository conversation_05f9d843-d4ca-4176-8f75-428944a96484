#!/usr/bin/env python3
"""
SS Network Dashboard Startup Script
Provides a simple way to start the dashboard with proper error handling
"""

import sys
import os
import subprocess
import time

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'flask',
        'flask-socketio',
        'colorama',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("📦 Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '-r', 'requirements.txt'
            ])
            print("✅ Dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("💡 Try running: pip install -r requirements.txt")
            return False
    
    print("✅ All dependencies are installed")
    return True

def start_dashboard():
    """Start the dashboard server"""
    print("🚀 Starting SS Network Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:1488")
    print("🔧 Server will listen on port 6666 for client connections")
    print("⚡ Press Ctrl+C to stop the dashboard")
    print("-" * 60)
    
    try:
        # Import and run the dashboard
        from dashboard import socketio, app
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("=" * 60)
    print("🔥 SS Network Dashboard Launcher")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists('dashboard.py'):
        print("❌ dashboard.py not found in current directory")
        print("💡 Make sure you're running this from the SS-BOTNET directory")
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Start the dashboard
    if not start_dashboard():
        sys.exit(1)

if __name__ == '__main__':
    main()
