# SS Network Dashboard

A modern Flask-based web dashboard for managing the SS Network botnet with a dark Bootstrap theme featuring red accents.

## Features

- **Modern Dark UI**: Bootstrap 5 dark theme with red accent colors
- **Real-time Updates**: WebSocket-based real-time client updates and logs
- **Client Management**: View connected clients, their status, and connection details
- **Command Execution**: Send commands to individual clients or broadcast to all
- **Quick Commands**: Pre-defined buttons for common commands (whoami, ipconfig, getip)
- **System Logs**: Real-time logging of all server activities
- **Command History**: Track all executed commands
- **Server Control**: Start/stop the server from the web interface

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Starting the Dashboard

1. Run the Flask dashboard:
```bash
python dashboard.py
```

2. Open your web browser and navigate to:
```
http://localhost:5000
```

3. Click "Start Server" to begin listening for client connections on port 6666

### Using the Original Server (Alternative)

You can still use the original command-line server:
```bash
python server.py
```

### Client Connection

Clients will connect using the existing client.py:
```bash
python client.py
```

## Dashboard Interface

### Main Components

1. **Stats Cards**: Display total souls, connected clients, commands sent, and server control
2. **Connected Souls Table**: Shows all connected clients with their details
3. **Command Center**: Execute commands on selected clients or broadcast to all
4. **Quick Commands**: One-click execution of common commands
5. **System Logs**: Real-time activity logging
6. **Command History**: Track of all executed commands

### Command Execution

- **Broadcast**: Send commands to all connected clients
- **Single Target**: Send commands to a specific client
- **Quick Commands**: Pre-defined commands for common tasks

### Security Features

- Command sanitization to prevent injection attacks
- Input validation
- Error handling and logging

## Configuration

### Server Settings

Default settings in `dashboard.py`:
- **Host**: 0.0.0.0 (all interfaces)
- **Port**: 6666 (client connections)
- **Dashboard Port**: 5000 (web interface)

### Customization

You can modify the following in `dashboard.py`:
- Server host and port
- Log retention (default: 1000 entries)
- Command history retention (default: 50 entries)

## File Structure

```
SS-BOTNET/
├── dashboard.py          # Flask dashboard server
├── server.py            # Original command-line server
├── client.py            # Client connection script
├── requirements.txt     # Python dependencies
├── README.md           # This file
└── templates/
    └── dashboard.html   # Web dashboard interface
```

## API Endpoints

The dashboard provides several API endpoints:

- `GET /api/clients` - Get connected clients
- `GET /api/logs` - Get system logs
- `GET /api/command_history` - Get command history
- `POST /api/server/start` - Start the server
- `POST /api/server/stop` - Stop the server
- `POST /api/command/broadcast` - Broadcast command to all clients
- `POST /api/command/single` - Send command to specific client

## WebSocket Events

Real-time updates via Socket.IO:
- `client_update` - Client connection/disconnection updates
- `new_log` - New log entries
- `command_response` - Command execution responses

## Security Considerations

- The dashboard is intended for authorized use only
- Commands are sanitized to prevent basic injection attacks
- Consider implementing authentication for production use
- Use HTTPS in production environments
- Restrict network access to authorized users only

## Troubleshooting

### Common Issues

1. **Port already in use**: Change the port in dashboard.py
2. **Permission denied**: Run with appropriate privileges
3. **Connection refused**: Check firewall settings
4. **Module not found**: Install requirements with pip

### Logs

Check the dashboard logs for detailed error information. All activities are logged in real-time through the web interface.

## License

This project is for educational purposes only. Use responsibly and in accordance with applicable laws and regulations.
